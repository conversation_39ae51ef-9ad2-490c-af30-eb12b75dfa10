<?php
    $vehicleContent = getContent('vehicle.content', true);
?>
<section class="our-vehicle bg-img py-120" data-background-image="<?php echo e(getImage('assets/images/frontend/vehicle/' . @$vehicleContent->data_values->background_image, '1905x1130')); ?>">
    <div class="container">
        <div class="section-heading">
            <p class="section-heading__name name-base"><?php echo e(__(@$vehicleContent->data_values->heading)); ?></p>
            <h3 class="section-heading__title text-white"><?php echo e(__(@$vehicleContent->data_values->subheading)); ?></h3>
        </div>
        <div class="row g-3 g-md-4">
            <?php $__currentLoopData = $vehicleTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $vehicleType): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="col-xsm-6 col-sm-6 col-lg-3">
                    <a href="<?php echo e(route('vehicles', $vehicleType->slug)); ?>" class="vehicle-card">
                        <div class="vehicle-card__thumb">
                            <img src="<?php echo e(getImage(getFilePath('vehicleType') . '/' . $vehicleType->image, getFileSize('vehicleType'))); ?>" alt="<?php echo app('translator')->get('image'); ?>" />
                        </div>
                        <div class="vehicle-card__content">
                            <h5 class="vehicle-card__name"><?php echo e(__($vehicleType->name)); ?></h5>
                            <p class="vehicle-card__desc"><?php echo e(__($vehicleType->description)); ?></p>
                        </div>
                    </a>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
</section>
<?php /**PATH C:\xampp\htdocs\ride\core\resources\views/templates/basic/sections/vehicle.blade.php ENDPATH**/ ?>