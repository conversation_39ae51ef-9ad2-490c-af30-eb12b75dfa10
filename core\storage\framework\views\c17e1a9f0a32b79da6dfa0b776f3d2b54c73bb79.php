<?php
    $vehicleTypes = App\Models\vehicleType::active()->get(['id', 'name']);
    $zones = App\Models\Zone::active()->get();
?>
<div class="search-form">
    <div class="container">
        <form action="<?php echo e(route('vehicles')); ?>" method="GET">
            <div class="search-form__wrapper">
                <div class="search-form-item">
                    <label class="form--label"><?php echo app('translator')->get('Select Vehicle'); ?></label>
                    <select class="form--control  select2-basic" name="vehicle_type_id">
                        <option value=""><?php echo app('translator')->get('Select One'); ?></option>
                        <?php $__currentLoopData = $vehicleTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $vehicleType): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($vehicleType->id); ?>"><?php echo e(__($vehicleType->name)); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>

                </div>
                <div class="search-form-item">
                    <label class="form--label"><?php echo app('translator')->get('Pick Up'); ?></label>
                    <select class="form--control  select2-basic" name="pick_up_zone_id" required>
                        <option value=""><?php echo app('translator')->get('Select One'); ?></option>
                        <?php $__currentLoopData = $zones; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $zone): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($zone->id); ?>"><?php echo e(__($zone->name)); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
                <div class="search-form-item">
                    <label class="form--label"><?php echo app('translator')->get('Date'); ?></label>
                    <input name="date" id="date" data-range="true" data-multiple-dates-separator=" - " type="text" data-language="en" class="datepicker-here form--control" data-position="bottom right" placeholder="<?php echo app('translator')->get('Start Date - End Date'); ?>" required autocomplete="off" />
                </div>
                <div class="search-form-item">
                    <label class="form--label"><?php echo app('translator')->get('Drop off'); ?></label>
                    <select class="form--control select2-basic" name="drop_off_zone_id" required>
                        <option value=""><?php echo app('translator')->get('Select One'); ?></option>
                        <?php $__currentLoopData = $zones; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $zone): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($zone->id); ?>"><?php echo e(__($zone->name)); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
                <div class="search-form__button">
                    <button class="btn btn--gradient"><span class="me-2"><i class="fas fa-search"></i></span> <?php echo app('translator')->get('Search'); ?></button>
                </div>
            </div>
        </form>
    </div>
</div>

<?php $__env->startPush('style-lib'); ?>
    <link rel="stylesheet" href="<?php echo e(asset('assets/global/css/datepicker.min.css')); ?>">
<?php $__env->stopPush(); ?>

<?php $__env->startPush('script-lib'); ?>
    <script src="<?php echo e(asset('assets/global/js/datepicker.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/global/js/datepicker.en.js')); ?>"></script>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('style'); ?>
    <style>
        #date {
            border: 1px solid hsl(var(--base-two)/0.1) !important;
        }

        #date:focus {
            border: 1px solid hsl(var(--base)) !important;
        }
    </style>
<?php $__env->stopPush(); ?>


<?php $__env->startPush('script'); ?>
    <script>
        (function($) {
            "use strict";
            $(".select2-basic").select2({
                width: "100%",
            });

            $('.datepicker-here').datepicker({
                changeYear: true,
                changeMonth: true,
                minDate: new Date(),
            });
        })(jQuery)
    </script>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\xampp\htdocs\ride\core\resources\views/templates/basic/sections/search.blade.php ENDPATH**/ ?>