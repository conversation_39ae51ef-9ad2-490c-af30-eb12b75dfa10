<?php
    $partnerElement = getContent('partner.element', false, null, true);
?>
<div class="py-120 brand-section bg-white">
    <div class="container">
        <div class="brand-section__slider">
            <div class="brand-slider">
                <?php $__currentLoopData = $partnerElement; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $partner): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="brand-slider-item">
                        <img src="<?php echo e(getImage('assets/images/frontend/partner/' . @$partner->data_values->image, '155x85')); ?>" alt="image" />
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </div>
</div>
<?php /**PATH C:\xampp\htdocs\ride\core\resources\views/templates/basic/sections/partner.blade.php ENDPATH**/ ?>